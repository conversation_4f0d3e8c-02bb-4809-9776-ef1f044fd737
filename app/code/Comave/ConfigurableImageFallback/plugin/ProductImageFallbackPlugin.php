<?php

namespace Comave\ConfigurableImageFallback\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable as ConfigurableResource;

class ProductImageFallbackPlugin
{
    private ProductRepositoryInterface $productRepository;
    private ConfigurableResource $configurableResource;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        ConfigurableResource $configurableResource
    ) {
        $this->productRepository = $productRepository;
        $this->configurableResource = $configurableResource;
    }

    public function afterGetMediaGalleryEntries(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        if (!empty($result) || $subject->getTypeId() !== ProductType::TYPE_SIMPLE) {
            return $result;
        }

        try {
            $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
            if (!empty($parentIds)) {
                $parent = $this->productRepository->getById($parentIds[0]);
                $parentEntries = $parent->getMediaGalleryEntries();

                if (!empty($parentEntries)) {
                    return $parentEntries;
                }
            }
        } catch (\Exception $e) {
            // Continue with original result if parent loading fails
        }

        return $result;
    }

    public function afterGetData(
        \Magento\Catalog\Model\Product $subject,
        $result,
        $key = '',
        $index = null
    ) {
        if (($key === 'image' || $key === 'small_image' || $key === 'thumbnail')
            && ($result === null || $result === 'no_selection' || empty($result))
            && $subject->getTypeId() === ProductType::TYPE_SIMPLE) {

            try {
                $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
                if (!empty($parentIds)) {
                    $parent = $this->productRepository->getById($parentIds[0]);
                    $parentImageData = $parent->getData($key);

                    if ($parentImageData && $parentImageData !== 'no_selection') {
                        return $parentImageData;
                    }
                }
            } catch (\Exception $e) {
                // Continue with original result if parent loading fails
            }
        }
        return $result;
    }

    public function afterGetImage(
        \Magento\Catalog\Model\Product $subject,
        $result
    ) {
        if (($result === null || $result === 'no_selection' || empty($result))
            && $subject->getTypeId() === ProductType::TYPE_SIMPLE) {

            try {
                $parentIds = $this->configurableResource->getParentIdsByChild($subject->getId());
                if (!empty($parentIds)) {
                    $parent = $this->productRepository->getById($parentIds[0]);
                    $parentImage = $parent->getImage();

                    if ($parentImage && $parentImage !== 'no_selection') {
                        return $parentImage;
                    }
                }
            } catch (\Exception $e) {
                // Continue with original result if parent loading fails
            }
        }

        return $result;
    }
}
