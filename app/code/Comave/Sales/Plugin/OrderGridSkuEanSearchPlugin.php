<?php

declare(strict_types=1);

namespace Comave\Sales\Plugin;

use Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;

class OrderGridSkuEanSearchPlugin
{
    /**
     * @param CollectionFactory $subject
     * @param Collection $collection
     * @param string $requestName
     * @return Collection
     */
    public function afterGetReport(
        CollectionFactory $subject,
        $collection,
        $requestName
    ) {
        if ($requestName !== 'sales_order_grid_data_source') {
            return $collection;
        }

        if ($collection->getMainTable() === $collection->getResource()->getTable('sales_order_grid')) {
            $this->addSkuEanSearchCapability($collection);
        }

        return $collection;
    }

    /**
     * Add SKU and EAN search capability to the collection
     *
     * @param Collection $collection
     * @return void
     */
    private function addSkuEanSearchCapability(Collection $collection): void
    {
        if ($collection->getFlag('sku_ean_capability_added')) {
            return;
        }

        $collection->setFlag('sku_ean_capability_added', true);

        // Join with sales_order_item table for SKU search
        $collection->getSelect()->joinLeft(
            ['sales_order_item' => $collection->getTable('sales_order_item')],
            'main_table.entity_id = sales_order_item.order_id',
            []
        );

        // Join with catalog_product_entity for EAN search
        $collection->getSelect()->joinLeft(
            ['cpe' => $collection->getTable('catalog_product_entity')],
            'sales_order_item.product_id = cpe.entity_id',
            []
        );

        // Join with EAN attribute value
        $eanAttributeId = $this->getEanAttributeId($collection);
        if ($eanAttributeId) {
            $collection->getSelect()->joinLeft(
                ['ean_attr' => $collection->getTable('catalog_product_entity_varchar')],
                'cpe.entity_id = ean_attr.entity_id AND ean_attr.attribute_id = ' . $eanAttributeId . ' AND ean_attr.store_id = 0',
                []
            );
        }

        // Group by order ID to prevent duplicates
        $collection->getSelect()->group('main_table.entity_id');

        // Add filter mapping for sku_ean field
        $this->addSkuEanFilterMapping($collection, $eanAttributeId);
    }

    /**
     * Add filter mapping for SKU/EAN search
     *
     * @param Collection $collection
     * @param int|null $eanAttributeId
     * @return void
     */
    private function addSkuEanFilterMapping(Collection $collection, ?int $eanAttributeId): void
    {
        // Create a custom expression for the sku_ean filter
        $conditions = ['IFNULL(sales_order_item.sku, "")'];

        if ($eanAttributeId) {
            $conditions[] = 'IFNULL(ean_attr.value, "")';
        }

        $expression = 'CONCAT_WS(" ", ' . implode(', ', $conditions) . ')';
        $collection->addFilterToMap('sku_ean', new \Zend_Db_Expr($expression));
    }

    /**
     * Get EAN attribute ID
     *
     * @param Collection $collection
     * @return int|null
     */
    private function getEanAttributeId(Collection $collection): ?int
    {
        try {
            $connection = $collection->getConnection();
            $select = $connection->select()
                ->from($collection->getTable('eav_attribute'), 'attribute_id')
                ->where('entity_type_id = ?', 4) // Product entity type
                ->where('attribute_code = ?', 'ean');

            $attributeId = $connection->fetchOne($select);
            return $attributeId ? (int)$attributeId : null;
        } catch (\Exception $e) {
            // If there's any error getting the EAN attribute, just return null
            // This will make the search work with SKU only
            return null;
        }
    }
}
