<?php

declare(strict_types=1);

namespace Comave\Sales\Test\Unit\Plugin;

use Comave\Sales\Plugin\OrderGridSkuEanSearchPlugin;
use Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory;
use Magento\Sales\Model\ResourceModel\Order\Grid\Collection;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;

class OrderGridSkuEanSearchPluginTest extends TestCase
{
    private OrderGridSkuEanSearchPlugin $plugin;
    private MockObject $collectionFactoryMock;
    private MockObject $collectionMock;

    protected function setUp(): void
    {
        $this->collectionFactoryMock = $this->createMock(CollectionFactory::class);
        $this->collectionMock = $this->createMock(Collection::class);
        $this->plugin = new OrderGridSkuEanSearchPlugin();
    }

    public function testAfterGetReportWithWrongRequestName(): void
    {
        $result = $this->plugin->afterGetReport(
            $this->collectionFactoryMock,
            $this->collectionMock,
            'wrong_request_name'
        );

        $this->assertSame($this->collectionMock, $result);
    }

    public function testAfterGetReportWithCorrectRequestName(): void
    {
        $resourceMock = $this->createMock(\Magento\Framework\Model\ResourceModel\Db\AbstractDb::class);
        $resourceMock->method('getTable')->willReturn('sales_order_grid');
        
        $this->collectionMock->method('getResource')->willReturn($resourceMock);
        $this->collectionMock->method('getMainTable')->willReturn('sales_order_grid');
        $this->collectionMock->method('getFlag')->willReturn(false);
        $this->collectionMock->method('setFlag')->willReturnSelf();
        $this->collectionMock->method('getTable')->willReturnArgument(0);
        
        $selectMock = $this->createMock(\Magento\Framework\DB\Select::class);
        $selectMock->method('joinLeft')->willReturnSelf();
        $selectMock->method('group')->willReturnSelf();
        
        $this->collectionMock->method('getSelect')->willReturn($selectMock);
        
        $connectionMock = $this->createMock(\Magento\Framework\DB\Adapter\AdapterInterface::class);
        $connectionMock->method('fetchOne')->willReturn('123'); // Mock EAN attribute ID
        $connectionMock->method('select')->willReturn($selectMock);
        
        $this->collectionMock->method('getConnection')->willReturn($connectionMock);

        $result = $this->plugin->afterGetReport(
            $this->collectionFactoryMock,
            $this->collectionMock,
            'sales_order_grid_data_source'
        );

        $this->assertSame($this->collectionMock, $result);
    }

    public function testAfterGetReportWithFlagAlreadySet(): void
    {
        $resourceMock = $this->createMock(\Magento\Framework\Model\ResourceModel\Db\AbstractDb::class);
        $resourceMock->method('getTable')->willReturn('sales_order_grid');
        
        $this->collectionMock->method('getResource')->willReturn($resourceMock);
        $this->collectionMock->method('getMainTable')->willReturn('sales_order_grid');
        $this->collectionMock->method('getFlag')->willReturn(true); // Flag already set
        
        // Should not call setFlag or any join methods
        $this->collectionMock->expects($this->never())->method('setFlag');
        $this->collectionMock->expects($this->never())->method('getSelect');

        $result = $this->plugin->afterGetReport(
            $this->collectionFactoryMock,
            $this->collectionMock,
            'sales_order_grid_data_source'
        );

        $this->assertSame($this->collectionMock, $result);
    }
}
