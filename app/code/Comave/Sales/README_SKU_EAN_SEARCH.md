# SKU/EAN Search Feature for Admin Orders Grid

## Overview
This feature adds SKU and EAN search capability to the admin orders grid at `/admin/sales/order/`. Administrators can now search for orders by entering a product SKU or EAN code in the new filter field.

## Implementation Details

### Files Created/Modified:

1. **Plugin**: `app/code/Comave/Sales/Plugin/OrderGridSkuEanSearchPlugin.php`
   - Intercepts the order grid collection
   - Adds joins with `sales_order_item` and product tables
   - Enables filtering by SKU and EAN values

2. **DI Configuration**: `app/code/Comave/Sales/etc/di.xml`
   - Registers the plugin for `CollectionFactory`

3. **UI Component**: `app/code/Comave/Sales/view/adminhtml/ui_component/sales_order_grid.xml`
   - Adds the SKU/EAN filter input field to the admin grid

### How It Works:

1. **Database Joins**: The plugin joins the order grid with:
   - `sales_order_item` table for SKU access
   - `catalog_product_entity` table for product linking
   - `catalog_product_entity_varchar` table for EAN attribute values

2. **Search Logic**: 
   - Searches in `sales_order_item.sku` field for SKU matches
   - Searches in EAN attribute values for EAN matches
   - Uses LIKE queries with wildcards for partial matches

3. **Performance**: 
   - Groups results by order ID to prevent duplicates
   - Only adds joins when the collection is accessed
   - Uses flags to prevent duplicate processing

## Testing Instructions

### Manual Testing:

1. **Access Admin Orders Grid**:
   - Go to Admin Panel → Sales → Orders
   - Look for the new "SKU/EAN" filter field in the filters section

2. **Test SKU Search**:
   - Enter a product SKU in the SKU/EAN filter field
   - Click "Apply Filters"
   - Verify that only orders containing products with that SKU are displayed

3. **Test EAN Search**:
   - Enter a product EAN code in the SKU/EAN filter field
   - Click "Apply Filters"
   - Verify that only orders containing products with that EAN are displayed

4. **Test Partial Matches**:
   - Enter partial SKU or EAN values
   - Verify that orders with matching products are found

5. **Test Combined with Other Filters**:
   - Use SKU/EAN filter along with date range, status, or other filters
   - Verify that all filters work together correctly

### Prerequisites for Testing:

1. **Products with EAN**: Ensure some products have EAN values set
2. **Orders with Products**: Create test orders with products that have both SKU and EAN
3. **Cache Clear**: Run `php bin/magento cache:clean` after installation

### Expected Behavior:

- ✅ Filter field appears in the admin orders grid
- ✅ Searching by SKU returns relevant orders
- ✅ Searching by EAN returns relevant orders
- ✅ Partial matches work (e.g., searching "ABC" finds "ABC123")
- ✅ No duplicate orders in results
- ✅ Works alongside other existing filters
- ✅ Performance remains acceptable

## Troubleshooting

### Common Issues:

1. **Filter not appearing**: 
   - Clear cache: `php bin/magento cache:clean`
   - Recompile DI: `php bin/magento setup:di:compile`

2. **No search results**:
   - Verify products have EAN attribute values set
   - Check that orders contain the searched products

3. **Performance issues**:
   - Consider adding database indexes on frequently searched fields
   - Monitor query performance in slow query logs

### Database Queries:
The plugin generates queries similar to:
```sql
SELECT main_table.* 
FROM sales_order_grid AS main_table
LEFT JOIN sales_order_item ON main_table.entity_id = sales_order_item.order_id
LEFT JOIN catalog_product_entity AS cpe ON sales_order_item.product_id = cpe.entity_id
LEFT JOIN catalog_product_entity_varchar AS ean_attr ON cpe.entity_id = ean_attr.entity_id AND ean_attr.attribute_id = [EAN_ATTRIBUTE_ID]
WHERE (sales_order_item.sku LIKE '%search_term%' OR ean_attr.value LIKE '%search_term%')
GROUP BY main_table.entity_id
```

## Future Enhancements

Potential improvements for future versions:
- Add separate SKU and EAN filter fields
- Include product name in search results
- Add search highlighting in results
- Implement search suggestions/autocomplete
- Add export functionality for filtered results
