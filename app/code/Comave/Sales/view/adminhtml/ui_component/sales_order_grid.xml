<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top">
        <filters name="listing_filters">
            <filterInput name="sku_ean" provider="${ $.parentName }" class="Magento\Ui\Component\Filters\Type\Input">
                <settings>
                    <dataScope>sku_ean</dataScope>
                    <label translate="true">SKU/EAN</label>
                    <placeholder translate="true">Search by SKU or EAN</placeholder>
                </settings>
            </filterInput>
        </filters>
    </listingToolbar>
</listing>
